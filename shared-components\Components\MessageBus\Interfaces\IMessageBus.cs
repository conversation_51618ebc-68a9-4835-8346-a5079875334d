using shared.Components.MessageBus.Models;

namespace shared.Components.MessageBus.Interfaces
{
    /// <summary>
    /// Interface for message bus service that replaces ApiEventBus with improved functionality
    /// </summary>
    public interface IMessageBus : IHostedService
    {
        /// <summary>
        /// Send a message to a target microservice
        /// </summary>
        /// <typeparam name="T">Type of the payload</typeparam>
        /// <param name="request">The message send request containing all parameters</param>
        /// <returns>True if message was queued successfully</returns>
        Task<bool> SendAsync<T>(MessageBusSendRequest<T> request);
    }
}
