using Microsoft.AspNetCore.Hosting.Server;
using Microsoft.AspNetCore.Hosting.Server.Features;
using Microsoft.Extensions.Options;
using shared.Authentication;
using shared.Components.MessageBus.Configuration;
using shared.Components.MessageBus.Interfaces;
using shared.Components.MessageBus.Models;
using shared.Models.Configuration;
using shared.Services;
using System.Collections.Concurrent;
using System.Reflection;
using System.Security.Claims;
using System.Text.Json;

namespace shared.Components.MessageBus
{
    /// <summary>
    /// Abstract base class for message bus implementations
    /// </summary>
    public abstract class MessageBusBase : IMessageBus
    {
        protected readonly ILogger<MessageBusBase> _logger;
        protected readonly ISecretsService _secretsService;
        protected readonly MicroserviceConfiguration _microserviceConfiguration;
        protected readonly MessageBusConfiguration _messageBusConfiguration;
        protected readonly IServiceProvider _serviceProvider;
        protected readonly IMessageBusQueueManager _queueManager;
        protected readonly IMessageDeduplicationService? _deduplicationService;
        protected readonly CancellationTokenSource _cancellationTokenSource = new();

        // Route cache for local method invocation
        protected readonly ConcurrentDictionary<string, MethodInfo> _routeCache = new();
        protected readonly ConcurrentDictionary<string, object> _controllerCache = new();

        protected MessageBusBase(
            ILogger<MessageBusBase> logger,
            ISecretsService secretsService,
            IOptions<MicroserviceConfiguration> microserviceConfiguration,
            IOptions<MessageBusConfiguration> messageBusConfiguration,
            IServiceProvider serviceProvider,
            IMessageBusQueueManager queueManager,
            IMessageDeduplicationService? deduplicationService = null)
        {
            _logger = logger;
            _secretsService = secretsService;
            _microserviceConfiguration = microserviceConfiguration.Value;
            _messageBusConfiguration = messageBusConfiguration.Value;
            _serviceProvider = serviceProvider;
            _queueManager = queueManager;
            _deduplicationService = deduplicationService;
        }

        /// <summary>
        /// Send a message to a target microservice
        /// </summary>
        public async Task<bool> SendAsync<T>(MessageBusSendRequest<T> request)
        {
            try
            {
                // Validate request
                if (!request.IsValid())
                {
                    _logger.LogError("Invalid message send request: {Request}", JsonSerializer.Serialize(request));
                    return false;
                }

                // Generate message ID
                var messageId = Guid.NewGuid().ToString();

                // Convert to transport message
                var transportMessage = CreateTransportMessage(request, messageId);

                // Check if this is a local message (same microservice)
                if (request.Target == _microserviceConfiguration.Type)
                {
                    var success = await ProcessLocalMessage(transportMessage);
                    return success != null;
                }
                else
                {
                    // Ensure target queue exists
                    if (!await _queueManager.EnsureQueueExistsAsync(request.Target))
                    {
                        _logger.LogError("Failed to ensure queue exists for target microservice {Target}", request.Target);
                        return false;
                    }

                    var success = await SendToExternalQueue(transportMessage);
                    return success != null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send message for request {Request}", JsonSerializer.Serialize(request));
                return false;
            }
        }



        /// <summary>
        /// Create transport message from send request
        /// </summary>
        protected virtual MessageBusMessage CreateTransportMessage<T>(MessageBusSendRequest<T> request, string messageId)
        {
            return new MessageBusMessage
            {
                Id = messageId,
                Source = _microserviceConfiguration.FullId,
                TargetMicroservice = request.Target,
                Controller = request.Controller,
                Route = request.Route,
                Method = request.Method,
                Payload = request.Payload!,
                Claims = request.Claims ?? new List<Claim>(),
                DelayInSeconds = request.DelayInSeconds,
                MaxRetries = request.MaxRetries,
                CreatedAt = DateTime.UtcNow,
                Metadata = request.Metadata ?? new Dictionary<string, object>()
            };
        }

        /// <summary>
        /// Process a message locally without going through external queue
        /// </summary>
        protected virtual async Task<string?> ProcessLocalMessage(MessageBusMessage message)
        {
            try
            {
                // Check deduplication if enabled
                if (_deduplicationService != null)
                {
                    if (!_deduplicationService.ShouldProcessMessage(message.Id))
                    {
                        _logger.LogInformation("Skipping duplicate message {MessageId}", message.Id);
                        return null;
                    }
                }

                // Apply delay if specified
                if (message.DelayInSeconds > 0)
                {
                    await Task.Delay(TimeSpan.FromSeconds(message.DelayInSeconds), _cancellationTokenSource.Token);
                }

                // Try to invoke method directly
                var success = await TryInvokeLocalMethod(message);

                if (success)
                {
                    message.MarkCompleted();

                    _logger.LogInformation("Successfully processed local message {MessageId} to {Endpoint}",
                        message.Id, message.GetEndpoint());
                    return message.Id;
                }
                else
                {
                    // Fallback to HTTP call if direct invocation fails
                    return await FallbackToHttpCall(message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process local message {MessageId}", message.Id);
                message.MarkFailed(ex.Message);

                return null;
            }
        }

        /// <summary>
        /// Try to invoke a local method directly without HTTP
        /// </summary>
        protected virtual async Task<bool> TryInvokeLocalMethod(MessageBusMessage message)
        {
            try
            {
                var routeKey = $"{message.Controller}.{message.Route}";
                
                // Try to get cached method
                if (!_routeCache.TryGetValue(routeKey, out var method))
                {
                    method = FindControllerMethod(message.Controller, message.Route);
                    if (method != null)
                    {
                        _routeCache[routeKey] = method;
                    }
                }

                if (method == null)
                {
                    _logger.LogWarning("Could not find method for route {Controller}/{Route}", message.Controller, message.Route);
                    return false;
                }

                // Get or create controller instance
                var controller = GetControllerInstance(message.Controller);
                if (controller == null)
                {
                    _logger.LogWarning("Could not create controller instance for {Controller}", message.Controller);
                    return false;
                }

                // Prepare parameters
                var parameters = await PrepareMethodParameters(method, message);
                
                // Invoke method
                var result = method.Invoke(controller, parameters);
                
                // Handle async methods
                if (result is Task task)
                {
                    await task;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to invoke local method for {Controller}/{Route}", message.Controller, message.Route);
                return false;
            }
        }

        /// <summary>
        /// Find controller method using reflection
        /// </summary>
        protected virtual MethodInfo? FindControllerMethod(string controllerName, string routeName)
        {
            try
            {
                // Look for controller types in loaded assemblies
                var controllerTypeName = $"{controllerName}Controller";
                var assemblies = AppDomain.CurrentDomain.GetAssemblies();
                
                foreach (var assembly in assemblies)
                {
                    var controllerType = assembly.GetTypes()
                        .FirstOrDefault(t => t.Name.Equals(controllerTypeName, StringComparison.OrdinalIgnoreCase) && 
                                           t.IsClass && !t.IsAbstract);
                    
                    if (controllerType != null)
                    {
                        // Look for method with matching route name
                        var method = controllerType.GetMethods(BindingFlags.Public | BindingFlags.Instance)
                            .FirstOrDefault(m => m.Name.Equals(routeName, StringComparison.OrdinalIgnoreCase));
                        
                        if (method != null)
                        {
                            return method;
                        }
                    }
                }
                
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding controller method {Controller}/{Route}", controllerName, routeName);
                return null;
            }
        }

        /// <summary>
        /// Get or create controller instance
        /// </summary>
        protected virtual object? GetControllerInstance(string controllerName)
        {
            var cacheKey = controllerName;
            
            if (_controllerCache.TryGetValue(cacheKey, out var cachedController))
            {
                return cachedController;
            }

            try
            {
                var controllerTypeName = $"{controllerName}Controller";
                var assemblies = AppDomain.CurrentDomain.GetAssemblies();
                
                foreach (var assembly in assemblies)
                {
                    var controllerType = assembly.GetTypes()
                        .FirstOrDefault(t => t.Name.Equals(controllerTypeName, StringComparison.OrdinalIgnoreCase) && 
                                           t.IsClass && !t.IsAbstract);
                    
                    if (controllerType != null)
                    {
                        // Try to create instance using DI
                        var controller = ActivatorUtilities.CreateInstance(_serviceProvider, controllerType);
                        _controllerCache[cacheKey] = controller;
                        return controller;
                    }
                }
                
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating controller instance for {Controller}", controllerName);
                return null;
            }
        }

        /// <summary>
        /// Prepare method parameters for invocation
        /// </summary>
        protected virtual async Task<object[]> PrepareMethodParameters(MethodInfo method, MessageBusMessage message)
        {
            var parameters = method.GetParameters();
            var args = new object[parameters.Length];

            for (int i = 0; i < parameters.Length; i++)
            {
                var param = parameters[i];

                // Check if parameter expects MessageBusMessage directly
                if (param.ParameterType == typeof(MessageBusMessage))
                {
                    args[i] = message;
                }
                else
                {
                    // Try to get payload as parameter type
                    try
                    {
                        var getPayloadMethod = typeof(MessageBusMessage).GetMethod(nameof(MessageBusMessage.GetPayload))!
                            .MakeGenericMethod(param.ParameterType);
                        args[i] = getPayloadMethod.Invoke(message, null)!;
                    }
                    catch
                    {
                        args[i] = param.HasDefaultValue ? param.DefaultValue! : Activator.CreateInstance(param.ParameterType)!;
                    }
                }
            }

            return args;
        }

        /// <summary>
        /// Fallback to HTTP call when direct invocation fails
        /// </summary>
        protected virtual async Task<string?> FallbackToHttpCall(MessageBusMessage message)
        {
            try
            {
                using var httpClient = new HttpClient();
                
                // Get base URI
                var server = _serviceProvider.GetService<IServer>();
                if (server?.Features.Get<IServerAddressesFeature>()?.Addresses.FirstOrDefault() is string address)
                {
                    var baseUri = new Uri(address);
                    
                    // Create JWT token
                    var bearer = await JWTHelper.GenerateJwtMicroService(_secretsService, _microserviceConfiguration, message.Claims);
                    
                    // Prepare request - send entire MessageBusMessage
                    var request = new HttpRequestMessage
                    {
                        Method = HttpMethod.Post, // Always POST for message bus messages
                        RequestUri = new Uri(baseUri, message.GetEndpoint()),
                        Content = new StringContent(JsonSerializer.Serialize(message), System.Text.Encoding.UTF8, "application/json")
                    };
                    request.Headers.Add("Authorization", $"Bearer {bearer}");
                    
                    _logger.LogInformation("Fallback HTTP call to {RequestUri}", request.RequestUri);
                    
                    var response = await httpClient.SendAsync(request);
                    
                    if (response.IsSuccessStatusCode)
                    {
                        message.MarkCompleted();
                        return message.Id;
                    }
                    else
                    {
                        var error = $"HTTP {response.StatusCode}: {await response.Content.ReadAsStringAsync()}";
                        message.MarkFailed(error);
                        return null;
                    }
                }
                else
                {
                    throw new InvalidOperationException("Could not determine application base URL");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Fallback HTTP call failed for message {MessageId}", message.Id);
                message.MarkFailed(ex.Message);
                return null;
            }
        }

        /// <summary>
        /// Send message to external queue - implemented by derived classes
        /// </summary>
        protected abstract Task<string?> SendToExternalQueue(MessageBusMessage message);

        /// <summary>
        /// Start the hosted service
        /// </summary>
        public abstract Task StartAsync(CancellationToken cancellationToken);

        /// <summary>
        /// Stop the hosted service
        /// </summary>
        public virtual Task StopAsync(CancellationToken cancellationToken)
        {
            _cancellationTokenSource.Cancel();
            return Task.CompletedTask;
        }

        /// <summary>
        /// Dispose resources
        /// </summary>
        public virtual void Dispose()
        {
            _cancellationTokenSource?.Dispose();
        }
    }
}
